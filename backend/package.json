{"name": "backend", "version": "1.0.0", "main": "server.js", "type": "module", "scripts": {"dev": "nodemon -r dotenv/config --experimental-json-modules server.js", "seed:admin": "node -r dotenv/config src/utils/seedAdmin.js", "seed:gamification": "node -r dotenv/config src/scripts/seedGamification.js", "import:playlist": "node -r dotenv/config src/utils/importPlaylistVideos.js", "import:playlist:direct": "node -r dotenv/config src/utils/importPlaylistVideosDirectly.js"}, "author": "", "license": "ISC", "description": "", "dependencies": {"axios": "^1.9.0", "bcrypt": "^5.1.1", "bcryptjs": "^2.4.3", "cloudinary": "^2.5.1", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^16.4.7", "express": "^4.21.2", "googleapis": "^148.0.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.9.5", "mongoose-aggregate-paginate-v2": "^1.1.3", "multer": "^1.4.5-lts.1", "nanoid": "^5.1.5", "nodemailer": "^6.10.1", "qrcode": "^1.5.4", "slugify": "^1.6.6"}, "devDependencies": {"nodemon": "^3.1.9", "prettier": "^3.4.2"}}